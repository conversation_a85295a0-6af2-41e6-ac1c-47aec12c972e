{"name": "illuminate/console", "description": "The Illuminate Console package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-mbstring": "*", "illuminate/collections": "^10.0", "illuminate/contracts": "^10.0", "illuminate/macroable": "^10.0", "laravel/prompts": "^0.1.9", "illuminate/support": "^10.0", "illuminate/view": "^10.0", "nunomaduro/termwind": "^1.13", "symfony/console": "^6.2", "symfony/process": "^6.2"}, "autoload": {"psr-4": {"Illuminate\\Console\\": ""}}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "suggest": {"ext-pcntl": "Required to use signal trapping.", "dragonmantank/cron-expression": "Required to use scheduler (^3.3.2).", "guzzlehttp/guzzle": "Required to use the ping methods on schedules (^7.5).", "illuminate/bus": "Required to use the scheduled job dispatcher (^10.0).", "illuminate/container": "Required to use the scheduler (^10.0).", "illuminate/filesystem": "Required to use the generator command (^10.0).", "illuminate/queue": "Required to use closures for scheduled jobs (^10.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}