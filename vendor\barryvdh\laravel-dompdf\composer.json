{"name": "barryvdh/laravel-dompdf", "description": "A DOMPDF Wrapper for Laravel", "license": "MIT", "keywords": ["laravel", "dompdf", "pdf"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "require": {"php": "^7.2 || ^8.0", "dompdf/dompdf": "^2.0.7", "illuminate/support": "^6|^7|^8|^9|^10|^11"}, "require-dev": {"orchestra/testbench": "^4|^5|^6|^7|^8|^9", "squizlabs/php_codesniffer": "^3.5", "phpro/grumphp": "^1 || ^2.5", "larastan/larastan": "^1.0|^2.7.0"}, "autoload": {"psr-4": {"Barryvdh\\DomPDF\\": "src"}}, "autoload-dev": {"psr-4": {"Barryvdh\\DomPDF\\Tests\\": "tests"}}, "extra": {"branch-alias": {"dev-master": "2.0-dev"}, "laravel": {"providers": ["Barryvdh\\DomPDF\\ServiceProvider"], "aliases": {"Pdf": "Barryvdh\\DomPDF\\Facade\\Pdf", "PDF": "Barryvdh\\DomPDF\\Facade\\Pdf"}}}, "scripts": {"test": "phpunit", "check-style": "phpcs -p --standard=psr12 src/", "fix-style": "phpcbf -p --standard=psr12 src/", "phpstan": "phpstan analyze --memory-limit=-1"}, "minimum-stability": "dev", "prefer-stable": true, "config": {"allow-plugins": {"phpro/grumphp": true}}}