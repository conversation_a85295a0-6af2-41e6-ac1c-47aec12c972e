[2025-07-21 13:53:18] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(317): Illuminate\\Log\\LogManager->error('The C:\\\\wamp64\\\\w...', Array)
#4 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Exception))
#5 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Exception))
#6 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Exception))
#7 C:\\wamp64\\www\\lavanderianovo\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-07-21 13:53:18] laravel.ERROR: The C:\wamp64\www\lavanderianovo\bootstrap\cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The C:\\wamp64\\www\\lavanderianovo\\bootstrap\\cache directory must be present and writable. at C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php:178)
[stacktrace]
#0 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 C:\\wamp64\\www\\lavanderianovo\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 C:\\wamp64\\www\\lavanderianovo\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
