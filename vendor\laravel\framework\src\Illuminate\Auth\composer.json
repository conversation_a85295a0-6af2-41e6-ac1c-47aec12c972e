{"name": "illuminate/auth", "description": "The Illuminate Auth package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-hash": "*", "illuminate/collections": "^10.0", "illuminate/contracts": "^10.0", "illuminate/http": "^10.0", "illuminate/macroable": "^10.0", "illuminate/queue": "^10.0", "illuminate/support": "^10.0"}, "autoload": {"psr-4": {"Illuminate\\Auth\\": ""}}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "suggest": {"illuminate/console": "Required to use the auth:clear-resets command (^10.0).", "illuminate/queue": "Required to fire login / logout events (^10.0).", "illuminate/session": "Required to use the session based guard (^10.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}